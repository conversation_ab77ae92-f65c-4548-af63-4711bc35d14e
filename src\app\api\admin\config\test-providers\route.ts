// Provider Testing API Endpoint

import { NextRequest, NextResponse } from 'next/server';
import { adminConfigService } from '@/lib/config/admin-config';
import { configManager } from '@/lib/config/configuration-manager';

// Middleware to check admin authentication
function checkAdminAuth(request: NextRequest): boolean {
  const adminApiKey = request.headers.get('x-admin-api-key');
  const expectedKey = process.env.ADMIN_API_KEY;
  
  return adminApiKey === expectedKey && expectedKey !== undefined;
}

// POST /api/admin/config/test-providers - Test specific providers
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    if (!checkAdminAuth(request)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { providers, detailed = false } = body;

    // Wait for configuration manager to be ready
    await configManager.waitForInitialization();

    const results: Record<string, any> = {};

    // If no specific providers requested, test all
    const providersToTest = providers || ['openai', 'openrouter', 'scrape-do'];

    for (const provider of providersToTest) {
      const startTime = Date.now();
      
      try {
        let testResult: any = {
          provider,
          success: false,
          responseTime: 0,
          error: null,
          timestamp: new Date().toISOString()
        };

        switch (provider) {
          case 'openai':
            testResult.success = await configManager.testProvider('openai');
            if (detailed && testResult.success) {
              testResult.details = await testOpenAIDetailed();
            }
            break;

          case 'openrouter':
            testResult.success = await configManager.testProvider('openrouter');
            if (detailed && testResult.success) {
              testResult.details = await testOpenRouterDetailed();
            }
            break;

          case 'scrape-do':
            testResult.success = await configManager.testProvider('scrape-do');
            if (detailed && testResult.success) {
              testResult.details = await testScrapeDoDetailed();
            }
            break;

          default:
            testResult.error = `Unknown provider: ${provider}`;
        }

        testResult.responseTime = Date.now() - startTime;
        results[provider] = testResult;

      } catch (error) {
        results[provider] = {
          provider,
          success: false,
          responseTime: Date.now() - startTime,
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        };
      }
    }

    // Calculate overall status
    const allProviders = Object.values(results);
    const successfulProviders = allProviders.filter((result: any) => result.success);
    const overallStatus = {
      totalProviders: allProviders.length,
      successfulProviders: successfulProviders.length,
      failedProviders: allProviders.length - successfulProviders.length,
      overallSuccess: successfulProviders.length === allProviders.length,
      averageResponseTime: allProviders.reduce((sum: number, result: any) => sum + result.responseTime, 0) / allProviders.length
    };

    return NextResponse.json({
      success: true,
      results,
      overallStatus,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Provider testing error:', error);
    return NextResponse.json(
      { 
        error: 'Provider testing failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET /api/admin/config/test-providers - Get provider test status
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    if (!checkAdminAuth(request)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const provider = searchParams.get('provider');
    const detailed = searchParams.get('detailed') === 'true';

    // Wait for configuration manager to be ready
    await configManager.waitForInitialization();

    if (provider) {
      // Test specific provider
      const startTime = Date.now();
      
      try {
        const success = await configManager.testProvider(provider as any);
        let details = null;

        if (detailed && success) {
          switch (provider) {
            case 'openai':
              details = await testOpenAIDetailed();
              break;
            case 'openrouter':
              details = await testOpenRouterDetailed();
              break;
            case 'scrape-do':
              details = await testScrapeDoDetailed();
              break;
          }
        }

        return NextResponse.json({
          success: true,
          result: {
            provider,
            success,
            responseTime: Date.now() - startTime,
            details,
            timestamp: new Date().toISOString()
          }
        });

      } catch (error) {
        return NextResponse.json({
          success: false,
          result: {
            provider,
            success: false,
            responseTime: Date.now() - startTime,
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
          }
        });
      }
    } else {
      // Test all providers
      const results = await adminConfigService.testProviders();
      
      return NextResponse.json({
        success: true,
        results,
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    console.error('Provider test status error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get provider test status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Detailed testing functions

async function testOpenAIDetailed(): Promise<any> {
  const apiKey = configManager.get<string>('OPENAI_API_KEY');
  
  try {
    // Test models endpoint
    const modelsResponse = await fetch('https://api.openai.com/v1/models', {
      headers: { 'Authorization': `Bearer ${apiKey}` }
    });
    
    if (!modelsResponse.ok) {
      throw new Error(`Models API failed: ${modelsResponse.status}`);
    }

    const models = await modelsResponse.json();
    const availableModels = models.data?.map((model: any) => model.id) || [];

    // Test a simple completion
    const completionResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: 'Test' }],
        max_tokens: 5
      })
    });

    const completionWorking = completionResponse.ok;

    return {
      modelsAvailable: availableModels.length,
      completionWorking,
      supportedModels: availableModels.filter((model: string) => 
        model.includes('gpt-4') || model.includes('gpt-3.5')
      )
    };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

async function testOpenRouterDetailed(): Promise<any> {
  const apiKey = configManager.get<string>('OPENROUTER_API_KEY');
  
  try {
    // Test models endpoint
    const modelsResponse = await fetch('https://openrouter.ai/api/v1/models', {
      headers: { 'Authorization': `Bearer ${apiKey}` }
    });
    
    if (!modelsResponse.ok) {
      throw new Error(`Models API failed: ${modelsResponse.status}`);
    }

    const models = await modelsResponse.json();
    const availableModels = models.data?.map((model: any) => model.id) || [];

    // Check for specific models we use
    const geminiAvailable = availableModels.some((model: string) => model.includes('gemini'));
    const claudeAvailable = availableModels.some((model: string) => model.includes('claude'));

    return {
      modelsAvailable: availableModels.length,
      geminiAvailable,
      claudeAvailable,
      supportedModels: availableModels.filter((model: string) => 
        model.includes('gemini') || model.includes('claude') || model.includes('gpt')
      ).slice(0, 10) // Limit to first 10 for brevity
    };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

async function testScrapeDoDetailed(): Promise<any> {
  const apiKey = configManager.get<string>('SCRAPE_DO_API_KEY');
  
  try {
    // Test with a simple URL
    const testResponse = await fetch(`https://api.scrape.do?token=${apiKey}&url=https://httpbin.org/json`);
    
    if (!testResponse.ok) {
      throw new Error(`Scrape.do API failed: ${testResponse.status}`);
    }

    const responseTime = testResponse.headers.get('x-response-time');
    const creditsUsed = testResponse.headers.get('x-credits-used');

    return {
      responseWorking: true,
      responseTime: responseTime || 'unknown',
      creditsUsed: creditsUsed || 'unknown',
      apiVersion: testResponse.headers.get('x-api-version') || 'unknown'
    };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
