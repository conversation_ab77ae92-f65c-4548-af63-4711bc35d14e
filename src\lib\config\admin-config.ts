// Admin-Specific Configuration Logic

import { AdminPanelConfiguration, ConfigurationChange, ValidationResult } from './types';
import { configManager } from './configuration-manager';

export class AdminConfigurationService {
  private configManager = configManager;

  constructor() {
    // Ensure configuration manager is initialized
    this.configManager.waitForInitialization();
  }

  /**
   * Get AI provider configuration
   */
  public getAIProviderConfig(): {
    openai: {
      enabled: boolean;
      model: string;
      maxTokens: number;
      temperature: number;
      timeout: number;
      priority: number;
    };
    openrouter: {
      enabled: boolean;
      model: string;
      maxTokens: number;
      temperature: number;
      implicitCaching: boolean;
      timeout: number;
      priority: number;
    };
    modelSelection: {
      strategy: string;
      fallbackOrder: string[];
      costThreshold: number;
      qualityThreshold: number;
    };
  } {
    return {
      openai: {
        enabled: this.configManager.get('aiGeneration.providers.openai.enabled'),
        model: this.configManager.get('aiGeneration.providers.openai.model'),
        maxTokens: this.configManager.get('aiGeneration.providers.openai.maxTokens'),
        temperature: this.configManager.get('aiGeneration.providers.openai.temperature'),
        timeout: this.configManager.get('aiGeneration.providers.openai.timeout'),
        priority: this.configManager.get('aiGeneration.providers.openai.priority')
      },
      openrouter: {
        enabled: this.configManager.get('aiGeneration.providers.openrouter.enabled'),
        model: this.configManager.get('aiGeneration.providers.openrouter.model'),
        maxTokens: this.configManager.get('aiGeneration.providers.openrouter.maxTokens'),
        temperature: this.configManager.get('aiGeneration.providers.openrouter.temperature'),
        implicitCaching: this.configManager.get('aiGeneration.providers.openrouter.implicitCaching'),
        timeout: this.configManager.get('aiGeneration.providers.openrouter.timeout'),
        priority: this.configManager.get('aiGeneration.providers.openrouter.priority')
      },
      modelSelection: {
        strategy: this.configManager.get('aiGeneration.modelSelection.strategy'),
        fallbackOrder: this.configManager.get('aiGeneration.modelSelection.fallbackOrder'),
        costThreshold: this.configManager.get('aiGeneration.modelSelection.costThreshold'),
        qualityThreshold: this.configManager.get('aiGeneration.modelSelection.qualityThreshold')
      }
    };
  }

  /**
   * Update AI provider configuration
   */
  public async updateAIProviderConfig(updates: Partial<{
    openai: Partial<{
      enabled: boolean;
      model: string;
      maxTokens: number;
      temperature: number;
      timeout: number;
      priority: number;
    }>;
    openrouter: Partial<{
      enabled: boolean;
      model: string;
      maxTokens: number;
      temperature: number;
      implicitCaching: boolean;
      timeout: number;
      priority: number;
    }>;
    modelSelection: Partial<{
      strategy: string;
      fallbackOrder: string[];
      costThreshold: number;
      qualityThreshold: number;
    }>;
  }>): Promise<ValidationResult> {
    const errors: string[] = [];

    try {
      // Update OpenAI configuration
      if (updates.openai) {
        for (const [key, value] of Object.entries(updates.openai)) {
          await this.configManager.set(`aiGeneration.providers.openai.${key}`, value);
        }
      }

      // Update OpenRouter configuration
      if (updates.openrouter) {
        for (const [key, value] of Object.entries(updates.openrouter)) {
          await this.configManager.set(`aiGeneration.providers.openrouter.${key}`, value);
        }
      }

      // Update model selection configuration
      if (updates.modelSelection) {
        for (const [key, value] of Object.entries(updates.modelSelection)) {
          await this.configManager.set(`aiGeneration.modelSelection.${key}`, value);
        }
      }

      return {
        isValid: true,
        errors: [],
        warnings: [],
        score: 100
      };
    } catch (error) {
      return {
        isValid: false,
        errors: [{ 
          path: 'aiProviders', 
          message: error instanceof Error ? error.message : 'Unknown error',
          value: updates,
          rule: { path: 'aiProviders', type: 'object', required: true }
        }],
        warnings: [],
        score: 0
      };
    }
  }

  /**
   * Get scraping configuration
   */
  public getScrapingConfig(): {
    scrapeDoConfig: {
      timeout: number;
      retryAttempts: number;
      costOptimization: {
        enabled: boolean;
        targetSavingsPercentage: number;
        creditThreshold: number;
        maxCreditsPerTool: number;
      };
      multiPageScraping: {
        enabled: boolean;
        maxPages: number;
        pageTypes: string[];
      };
    };
    mediaExtraction: {
      ogImageEnabled: boolean;
      faviconEnabled: boolean;
      screenshotFallback: boolean;
      imageQuality: number;
      maxImageSize: number;
    };
  } {
    return {
      scrapeDoConfig: {
        timeout: this.configManager.get('scraping.scrapeDoConfig.timeout'),
        retryAttempts: this.configManager.get('scraping.scrapeDoConfig.retryAttempts'),
        costOptimization: {
          enabled: this.configManager.get('scraping.scrapeDoConfig.costOptimization.enabled'),
          targetSavingsPercentage: this.configManager.get('scraping.scrapeDoConfig.costOptimization.targetSavingsPercentage'),
          creditThreshold: this.configManager.get('scraping.scrapeDoConfig.costOptimization.creditThreshold'),
          maxCreditsPerTool: this.configManager.get('scraping.scrapeDoConfig.costOptimization.maxCreditsPerTool')
        },
        multiPageScraping: {
          enabled: this.configManager.get('scraping.scrapeDoConfig.multiPageScraping.enabled'),
          maxPages: this.configManager.get('scraping.scrapeDoConfig.multiPageScraping.maxPages'),
          pageTypes: this.configManager.get('scraping.scrapeDoConfig.multiPageScraping.pageTypes')
        }
      },
      mediaExtraction: {
        ogImageEnabled: this.configManager.get('scraping.mediaExtraction.ogImageEnabled'),
        faviconEnabled: this.configManager.get('scraping.mediaExtraction.faviconEnabled'),
        screenshotFallback: this.configManager.get('scraping.mediaExtraction.screenshotFallback'),
        imageQuality: this.configManager.get('scraping.mediaExtraction.imageQuality'),
        maxImageSize: this.configManager.get('scraping.mediaExtraction.maxImageSize')
      }
    };
  }

  /**
   * Get system configuration
   */
  public getSystemConfig(): {
    contentQualityThreshold: number;
    autoApprovalEnabled: boolean;
    debugMode: boolean;
    maintenanceMode: boolean;
    security: {
      apiKeyRotationDays: number;
      sessionTimeoutMinutes: number;
      maxLoginAttempts: number;
      auditLogging: boolean;
    };
    performance: {
      cacheEnabled: boolean;
      cacheTTL: number;
      rateLimiting: boolean;
      requestsPerMinute: number;
    };
  } {
    return {
      contentQualityThreshold: this.configManager.get('system.contentQualityThreshold'),
      autoApprovalEnabled: this.configManager.get('system.autoApprovalEnabled'),
      debugMode: this.configManager.get('system.debugMode'),
      maintenanceMode: this.configManager.get('system.maintenanceMode'),
      security: {
        apiKeyRotationDays: this.configManager.get('system.security.apiKeyRotationDays'),
        sessionTimeoutMinutes: this.configManager.get('system.security.sessionTimeoutMinutes'),
        maxLoginAttempts: this.configManager.get('system.security.maxLoginAttempts'),
        auditLogging: this.configManager.get('system.security.auditLogging')
      },
      performance: {
        cacheEnabled: this.configManager.get('system.performance.cacheEnabled'),
        cacheTTL: this.configManager.get('system.performance.cacheTTL'),
        rateLimiting: this.configManager.get('system.performance.rateLimiting'),
        requestsPerMinute: this.configManager.get('system.performance.requestsPerMinute')
      }
    };
  }

  /**
   * Update system configuration
   */
  public async updateSystemConfig(updates: Partial<{
    contentQualityThreshold: number;
    autoApprovalEnabled: boolean;
    debugMode: boolean;
    maintenanceMode: boolean;
    security: Partial<{
      apiKeyRotationDays: number;
      sessionTimeoutMinutes: number;
      maxLoginAttempts: number;
      auditLogging: boolean;
    }>;
    performance: Partial<{
      cacheEnabled: boolean;
      cacheTTL: number;
      rateLimiting: boolean;
      requestsPerMinute: number;
    }>;
  }>): Promise<ValidationResult> {
    try {
      // Update top-level system settings
      const topLevelFields = ['contentQualityThreshold', 'autoApprovalEnabled', 'debugMode', 'maintenanceMode'];
      for (const field of topLevelFields) {
        if (updates[field as keyof typeof updates] !== undefined) {
          await this.configManager.set(`system.${field}`, updates[field as keyof typeof updates]);
        }
      }

      // Update security settings
      if (updates.security) {
        for (const [key, value] of Object.entries(updates.security)) {
          await this.configManager.set(`system.security.${key}`, value);
        }
      }

      // Update performance settings
      if (updates.performance) {
        for (const [key, value] of Object.entries(updates.performance)) {
          await this.configManager.set(`system.performance.${key}`, value);
        }
      }

      return {
        isValid: true,
        errors: [],
        warnings: [],
        score: 100
      };
    } catch (error) {
      return {
        isValid: false,
        errors: [{ 
          path: 'system', 
          message: error instanceof Error ? error.message : 'Unknown error',
          value: updates,
          rule: { path: 'system', type: 'object', required: true }
        }],
        warnings: [],
        score: 0
      };
    }
  }

  /**
   * Test all configured providers
   */
  public async testProviders(): Promise<{
    openai: { success: boolean; error?: string };
    openrouter: { success: boolean; error?: string };
    scrapeDoAPI: { success: boolean; error?: string };
  }> {
    const results = {
      openai: { success: false, error: undefined as string | undefined },
      openrouter: { success: false, error: undefined as string | undefined },
      scrapeDoAPI: { success: false, error: undefined as string | undefined }
    };

    try {
      results.openai.success = await this.configManager.testProvider('openai');
    } catch (error) {
      results.openai.error = error instanceof Error ? error.message : 'Unknown error';
    }

    try {
      results.openrouter.success = await this.configManager.testProvider('openrouter');
    } catch (error) {
      results.openrouter.error = error instanceof Error ? error.message : 'Unknown error';
    }

    try {
      results.scrapeDoAPI.success = await this.configManager.testProvider('scrape-do');
    } catch (error) {
      results.scrapeDoAPI.error = error instanceof Error ? error.message : 'Unknown error';
    }

    return results;
  }

  /**
   * Get configuration change history
   */
  public getChangeHistory(limit = 50): ConfigurationChange[] {
    return this.configManager.getChanges().slice(-limit);
  }

  /**
   * Rollback a configuration change
   */
  public async rollbackChange(changeId: string): Promise<void> {
    await this.configManager.rollback(changeId);
  }

  /**
   * Export configuration for backup
   */
  public async exportConfiguration(includeSecrets = false): Promise<string> {
    return this.configManager.exportConfiguration(includeSecrets);
  }

  /**
   * Import configuration from backup
   */
  public async importConfiguration(configJson: string): Promise<ValidationResult> {
    try {
      const config = JSON.parse(configJson);
      return await this.configManager.import(config);
    } catch (error) {
      return {
        isValid: false,
        errors: [{ 
          path: 'import', 
          message: `Invalid JSON: ${error instanceof Error ? error.message : 'Unknown error'}`,
          value: configJson,
          rule: { path: 'import', type: 'object', required: true }
        }],
        warnings: [],
        score: 0
      };
    }
  }

  /**
   * Get configuration summary for dashboard
   */
  public getConfigurationSummary(): {
    environment: string;
    providersEnabled: string[];
    featuresEnabled: string[];
    lastUpdated: string;
    validationScore: number;
    healthStatus: 'healthy' | 'warning' | 'error';
  } {
    const summary = this.configManager.getConfigurationSummary();
    
    // Determine health status
    let healthStatus: 'healthy' | 'warning' | 'error' = 'healthy';
    
    if (summary.providersEnabled.length === 0) {
      healthStatus = 'error';
    } else if (summary.providersEnabled.length === 1) {
      healthStatus = 'warning';
    }

    return {
      ...summary,
      healthStatus
    };
  }

  /**
   * Validate current configuration
   */
  public async validateConfiguration(): Promise<ValidationResult> {
    return this.configManager.validate();
  }
}

// Export singleton instance
export const adminConfigService = new AdminConfigurationService();
