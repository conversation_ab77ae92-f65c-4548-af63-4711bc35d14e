'use client';

import { useState, useEffect } from 'react';

interface SystemConfigProps {
  onSave: () => void;
}

interface SystemConfigData {
  contentQualityThreshold: number;
  autoApprovalEnabled: boolean;
  debugMode: boolean;
  maintenanceMode: boolean;
  security: {
    apiKeyRotationDays: number;
    sessionTimeoutMinutes: number;
    maxLoginAttempts: number;
    auditLogging: boolean;
  };
  performance: {
    cacheEnabled: boolean;
    cacheTTL: number;
    rateLimiting: boolean;
    requestsPerMinute: number;
  };
}

export function SystemConfig({ onSave }: SystemConfigProps) {
  const [config, setConfig] = useState<SystemConfigData | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadConfiguration();
  }, []);

  const loadConfiguration = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/config?section=system', {
        headers: {
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to load system configuration');
      }

      const data = await response.json();
      setConfig(data.data);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!config) return;

    try {
      setSaving(true);
      setError(null);

      const response = await fetch('/api/admin/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        },
        body: JSON.stringify({
          section: 'system',
          action: 'update',
          data: config
        })
      });

      if (!response.ok) {
        throw new Error('Failed to save system configuration');
      }

      const result = await response.json();
      
      if (result.validation && !result.validation.isValid) {
        throw new Error(`Validation failed: ${result.validation.errors.map((e: any) => e.message).join(', ')}`);
      }

      onSave();
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setSaving(false);
    }
  };

  const updateConfig = (path: string, value: any) => {
    if (!config) return;

    const keys = path.split('.');
    const newConfig = { ...config };
    let current: any = newConfig;

    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;

    setConfig(newConfig);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-gray-400">Loading system configuration...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/20 border border-red-700 rounded-lg p-4">
        <div className="text-red-400 font-semibold">Error</div>
        <div className="text-red-300 mt-1">{error}</div>
        <button
          onClick={loadConfiguration}
          className="mt-3 bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-sm"
        >
          Retry
        </button>
      </div>
    );
  }

  if (!config) return null;

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-2">System Configuration</h3>
        <p className="text-gray-400">
          Configure system-wide settings, performance, and operational parameters.
        </p>
      </div>

      {/* General System Settings */}
      <div className="bg-zinc-700 rounded-lg p-6">
        <h4 className="font-semibold mb-4">General Settings</h4>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium mb-2">Content Quality Threshold</label>
            <input
              type="number"
              value={config.contentQualityThreshold}
              onChange={(e) => updateConfig('contentQualityThreshold', parseFloat(e.target.value))}
              min="0"
              max="1"
              step="0.01"
              className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
            />
            <p className="text-xs text-gray-400 mt-1">
              Minimum quality score (0-1) for auto-approval
            </p>
          </div>

          <div className="space-y-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={config.autoApprovalEnabled}
                onChange={(e) => updateConfig('autoApprovalEnabled', e.target.checked)}
                className="rounded bg-zinc-600 border-zinc-500 text-orange-500 focus:ring-orange-500"
              />
              <span className="text-sm">Enable Auto-Approval</span>
            </label>

            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={config.debugMode}
                onChange={(e) => updateConfig('debugMode', e.target.checked)}
                className="rounded bg-zinc-600 border-zinc-500 text-orange-500 focus:ring-orange-500"
              />
              <span className="text-sm">Debug Mode</span>
            </label>

            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={config.maintenanceMode}
                onChange={(e) => updateConfig('maintenanceMode', e.target.checked)}
                className="rounded bg-zinc-600 border-zinc-500 text-orange-500 focus:ring-orange-500"
              />
              <span className="text-sm">Maintenance Mode</span>
            </label>
          </div>
        </div>
      </div>

      {/* Security Settings */}
      <div className="bg-zinc-700 rounded-lg p-6">
        <h4 className="font-semibold mb-4">Security Settings</h4>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">API Key Rotation (Days)</label>
            <input
              type="number"
              value={config.security.apiKeyRotationDays}
              onChange={(e) => updateConfig('security.apiKeyRotationDays', parseInt(e.target.value))}
              min="1"
              max="365"
              className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Session Timeout (Minutes)</label>
            <input
              type="number"
              value={config.security.sessionTimeoutMinutes}
              onChange={(e) => updateConfig('security.sessionTimeoutMinutes', parseInt(e.target.value))}
              min="5"
              max="1440"
              className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Max Login Attempts</label>
            <input
              type="number"
              value={config.security.maxLoginAttempts}
              onChange={(e) => updateConfig('security.maxLoginAttempts', parseInt(e.target.value))}
              min="1"
              max="20"
              className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
            />
          </div>

          <div className="flex items-center">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={config.security.auditLogging}
                onChange={(e) => updateConfig('security.auditLogging', e.target.checked)}
                className="rounded bg-zinc-600 border-zinc-500 text-orange-500 focus:ring-orange-500"
              />
              <span className="text-sm">Enable Audit Logging</span>
            </label>
          </div>
        </div>
      </div>

      {/* Performance Settings */}
      <div className="bg-zinc-700 rounded-lg p-6">
        <h4 className="font-semibold mb-4">Performance Settings</h4>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Cache TTL (Seconds)</label>
            <input
              type="number"
              value={config.performance.cacheTTL}
              onChange={(e) => updateConfig('performance.cacheTTL', parseInt(e.target.value))}
              min="60"
              max="86400"
              className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Requests Per Minute</label>
            <input
              type="number"
              value={config.performance.requestsPerMinute}
              onChange={(e) => updateConfig('performance.requestsPerMinute', parseInt(e.target.value))}
              min="1"
              max="10000"
              className="w-full bg-zinc-600 border border-zinc-500 rounded-lg px-3 py-2 text-white"
            />
          </div>

          <div className="flex items-center">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={config.performance.cacheEnabled}
                onChange={(e) => updateConfig('performance.cacheEnabled', e.target.checked)}
                className="rounded bg-zinc-600 border-zinc-500 text-orange-500 focus:ring-orange-500"
              />
              <span className="text-sm">Enable Caching</span>
            </label>
          </div>

          <div className="flex items-center">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={config.performance.rateLimiting}
                onChange={(e) => updateConfig('performance.rateLimiting', e.target.checked)}
                className="rounded bg-zinc-600 border-zinc-500 text-orange-500 focus:ring-orange-500"
              />
              <span className="text-sm">Enable Rate Limiting</span>
            </label>
          </div>
        </div>
      </div>

      {/* Warning Messages */}
      {config.maintenanceMode && (
        <div className="bg-yellow-900/20 border border-yellow-700 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <span className="text-yellow-400">⚠️</span>
            <span className="text-yellow-300 font-medium">Maintenance Mode Enabled</span>
          </div>
          <p className="text-yellow-200 text-sm mt-1">
            The system is currently in maintenance mode. Users may experience limited functionality.
          </p>
        </div>
      )}

      {!config.performance.cacheEnabled && (
        <div className="bg-blue-900/20 border border-blue-700 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <span className="text-blue-400">ℹ️</span>
            <span className="text-blue-300 font-medium">Caching Disabled</span>
          </div>
          <p className="text-blue-200 text-sm mt-1">
            Disabling caching may impact system performance. Consider enabling it for better response times.
          </p>
        </div>
      )}

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          onClick={handleSave}
          disabled={saving}
          className="bg-orange-500 hover:bg-orange-600 disabled:bg-orange-700 px-6 py-2 rounded-lg font-medium transition-colors"
        >
          {saving ? 'Saving...' : 'Save System Settings'}
        </button>
      </div>
    </div>
  );
}
