// AI System Integration with Configuration Manager

import { configManager } from './configuration-manager';
import { AIModelConfig, AIProvider } from '../ai/types';

export class AIConfigurationIntegration {
  private configManager = configManager;

  constructor() {
    // Ensure configuration manager is initialized
    this.configManager.waitForInitialization();
  }

  /**
   * Get OpenAI configuration from config manager
   */
  public getOpenAIConfig(): AIModelConfig {
    return {
      provider: 'openai' as AIProvider,
      model: this.configManager.get('aiGeneration.providers.openai.model') || 'gpt-4o-2024-11-20',
      maxInputTokens: 128000, // GPT-4o input limit
      maxOutputTokens: this.configManager.get('aiGeneration.providers.openai.maxTokens') || 16384,
      temperature: this.configManager.get('aiGeneration.providers.openai.temperature') || 0.7,
      timeout: this.configManager.get('aiGeneration.providers.openai.timeout') || 60000,
      retryAttempts: 3,
      structuredOutput: true,
      multiPageSupport: true
    };
  }

  /**
   * Get OpenRouter configuration from config manager
   */
  public getOpenRouterConfig(): AIModelConfig {
    return {
      provider: 'openrouter' as AIProvider,
      model: this.configManager.get('aiGeneration.providers.openrouter.model') || 'google/gemini-2.5-pro-preview',
      maxInputTokens: 1048576, // ~1M tokens input context
      maxOutputTokens: this.configManager.get('aiGeneration.providers.openrouter.maxTokens') || 65536,
      temperature: this.configManager.get('aiGeneration.providers.openrouter.temperature') || 0.7,
      timeout: this.configManager.get('aiGeneration.providers.openrouter.timeout') || 120000,
      retryAttempts: 3,
      structuredOutput: true,
      multiPageSupport: true,
      extraHeaders: {
        'HTTP-Referer': this.configManager.get('SITE_URL') || 'https://aidude.com',
        'X-Title': 'AI Dude Directory'
      }
    };
  }

  /**
   * Check if OpenAI provider is enabled
   */
  public isOpenAIEnabled(): boolean {
    return this.configManager.get('aiGeneration.providers.openai.enabled') ?? true;
  }

  /**
   * Check if OpenRouter provider is enabled
   */
  public isOpenRouterEnabled(): boolean {
    return this.configManager.get('aiGeneration.providers.openrouter.enabled') ?? true;
  }

  /**
   * Get model selection strategy
   */
  public getModelSelectionStrategy(): {
    strategy: 'auto' | 'manual' | 'cost_optimized' | 'quality_optimized';
    fallbackOrder: string[];
    costThreshold: number;
    qualityThreshold: number;
  } {
    return {
      strategy: this.configManager.get('aiGeneration.modelSelection.strategy') || 'auto',
      fallbackOrder: this.configManager.get('aiGeneration.modelSelection.fallbackOrder') || ['openai', 'openrouter'],
      costThreshold: this.configManager.get('aiGeneration.modelSelection.costThreshold') || 0.01,
      qualityThreshold: this.configManager.get('aiGeneration.modelSelection.qualityThreshold') || 0.8
    };
  }

  /**
   * Get content generation settings
   */
  public getContentGenerationSettings(): {
    autoApproval: boolean;
    qualityThreshold: number;
    editorialReviewRequired: boolean;
    maxRetries: number;
    timeoutSeconds: number;
  } {
    return {
      autoApproval: this.configManager.get('aiGeneration.contentGeneration.autoApproval') ?? false,
      qualityThreshold: this.configManager.get('aiGeneration.contentGeneration.qualityThreshold') || 0.8,
      editorialReviewRequired: this.configManager.get('aiGeneration.contentGeneration.editorialReviewRequired') ?? true,
      maxRetries: this.configManager.get('aiGeneration.contentGeneration.maxRetries') || 3,
      timeoutSeconds: this.configManager.get('aiGeneration.contentGeneration.timeoutSeconds') || 300
    };
  }

  /**
   * Get API keys from configuration
   */
  public getAPIKeys(): {
    openai: string;
    openrouter: string;
  } {
    return {
      openai: this.configManager.get('OPENAI_API_KEY') || '',
      openrouter: this.configManager.get('OPENROUTER_API_KEY') || ''
    };
  }

  /**
   * Get provider priorities
   */
  public getProviderPriorities(): {
    openai: number;
    openrouter: number;
  } {
    return {
      openai: this.configManager.get('aiGeneration.providers.openai.priority') || 1,
      openrouter: this.configManager.get('aiGeneration.providers.openrouter.priority') || 2
    };
  }

  /**
   * Get available providers based on configuration
   */
  public getAvailableProviders(): AIProvider[] {
    const providers: AIProvider[] = [];
    
    if (this.isOpenAIEnabled() && this.getAPIKeys().openai) {
      providers.push('openai');
    }
    
    if (this.isOpenRouterEnabled() && this.getAPIKeys().openrouter) {
      providers.push('openrouter');
    }
    
    return providers;
  }

  /**
   * Get preferred provider based on strategy and priorities
   */
  public getPreferredProvider(): AIProvider | null {
    const strategy = this.getModelSelectionStrategy();
    const priorities = this.getProviderPriorities();
    const availableProviders = this.getAvailableProviders();

    if (availableProviders.length === 0) {
      return null;
    }

    switch (strategy.strategy) {
      case 'cost_optimized':
        // OpenRouter typically has better pricing for large models
        return availableProviders.includes('openrouter') ? 'openrouter' : availableProviders[0];
      
      case 'quality_optimized':
        // OpenAI typically has more consistent quality
        return availableProviders.includes('openai') ? 'openai' : availableProviders[0];
      
      case 'manual':
        // Use fallback order
        for (const provider of strategy.fallbackOrder) {
          if (availableProviders.includes(provider as AIProvider)) {
            return provider as AIProvider;
          }
        }
        return availableProviders[0];
      
      case 'auto':
      default:
        // Use priority-based selection
        const sortedProviders = availableProviders.sort((a, b) => {
          const priorityA = priorities[a] || 999;
          const priorityB = priorities[b] || 999;
          return priorityA - priorityB;
        });
        return sortedProviders[0];
    }
  }

  /**
   * Get configuration for specific provider
   */
  public getProviderConfig(provider: AIProvider): AIModelConfig {
    switch (provider) {
      case 'openai':
        return this.getOpenAIConfig();
      case 'openrouter':
        return this.getOpenRouterConfig();
      default:
        throw new Error(`Unknown provider: ${provider}`);
    }
  }

  /**
   * Validate AI configuration
   */
  public validateAIConfiguration(): {
    valid: boolean;
    issues: string[];
    warnings: string[];
  } {
    const issues: string[] = [];
    const warnings: string[] = [];

    // Check if at least one provider is enabled and configured
    const availableProviders = this.getAvailableProviders();
    if (availableProviders.length === 0) {
      issues.push('No AI providers are enabled or properly configured');
    }

    // Check API keys
    const apiKeys = this.getAPIKeys();
    if (this.isOpenAIEnabled() && !apiKeys.openai) {
      issues.push('OpenAI is enabled but API key is missing');
    }
    if (this.isOpenRouterEnabled() && !apiKeys.openrouter) {
      issues.push('OpenRouter is enabled but API key is missing');
    }

    // Check configuration values
    const openaiConfig = this.getOpenAIConfig();
    const openrouterConfig = this.getOpenRouterConfig();

    if (openaiConfig.maxOutputTokens > 16384) {
      warnings.push('OpenAI maxOutputTokens exceeds recommended limit of 16384');
    }
    if (openrouterConfig.maxOutputTokens > 65536) {
      warnings.push('OpenRouter maxOutputTokens exceeds recommended limit of 65536');
    }

    if (openaiConfig.temperature < 0 || openaiConfig.temperature > 2) {
      issues.push('OpenAI temperature must be between 0 and 2');
    }
    if (openrouterConfig.temperature < 0 || openrouterConfig.temperature > 2) {
      issues.push('OpenRouter temperature must be between 0 and 2');
    }

    // Check content generation settings
    const contentSettings = this.getContentGenerationSettings();
    if (contentSettings.qualityThreshold < 0 || contentSettings.qualityThreshold > 1) {
      issues.push('Quality threshold must be between 0 and 1');
    }

    if (contentSettings.autoApproval && !contentSettings.editorialReviewRequired) {
      warnings.push('Auto-approval is enabled without editorial review - this may compromise content quality');
    }

    return {
      valid: issues.length === 0,
      issues,
      warnings
    };
  }

  /**
   * Get system configuration summary for AI components
   */
  public getAISystemSummary(): {
    providersEnabled: string[];
    preferredProvider: string | null;
    strategy: string;
    contentGeneration: {
      autoApproval: boolean;
      editorialReview: boolean;
      qualityThreshold: number;
    };
    validation: {
      valid: boolean;
      issueCount: number;
      warningCount: number;
    };
  } {
    const availableProviders = this.getAvailableProviders();
    const preferredProvider = this.getPreferredProvider();
    const strategy = this.getModelSelectionStrategy();
    const contentSettings = this.getContentGenerationSettings();
    const validation = this.validateAIConfiguration();

    return {
      providersEnabled: availableProviders,
      preferredProvider,
      strategy: strategy.strategy,
      contentGeneration: {
        autoApproval: contentSettings.autoApproval,
        editorialReview: contentSettings.editorialReviewRequired,
        qualityThreshold: contentSettings.qualityThreshold
      },
      validation: {
        valid: validation.valid,
        issueCount: validation.issues.length,
        warningCount: validation.warnings.length
      }
    };
  }

  /**
   * Listen for configuration changes
   */
  public onConfigurationChange(callback: (path: string, value: any) => void): void {
    this.configManager.on('change', ({ path, value }) => {
      // Only notify for AI-related configuration changes
      if (path.startsWith('aiGeneration.') || 
          path === 'OPENAI_API_KEY' || 
          path === 'OPENROUTER_API_KEY') {
        callback(path, value);
      }
    });
  }

  /**
   * Update AI provider configuration
   */
  public async updateProviderConfig(
    provider: AIProvider, 
    config: Partial<{
      enabled: boolean;
      model: string;
      maxTokens: number;
      temperature: number;
      timeout: number;
      priority: number;
    }>
  ): Promise<void> {
    for (const [key, value] of Object.entries(config)) {
      await this.configManager.set(`aiGeneration.providers.${provider}.${key}`, value);
    }
  }
}

// Export singleton instance
export const aiConfigIntegration = new AIConfigurationIntegration();
