// Configuration Validation API Endpoint

import { NextRequest, NextResponse } from 'next/server';
import { adminConfigService } from '@/lib/config/admin-config';
import { configManager } from '@/lib/config/configuration-manager';

// Middleware to check admin authentication
function checkAdminAuth(request: NextRequest): boolean {
  const adminApiKey = request.headers.get('x-admin-api-key');
  const expectedKey = process.env.ADMIN_API_KEY;
  
  return adminApiKey === expectedKey && expectedKey !== undefined;
}

// POST /api/admin/config/validate - Validate configuration
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    if (!checkAdminAuth(request)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { type, data, path, value } = body;

    // Wait for configuration manager to be ready
    await configManager.waitForInitialization();

    let validation: any;

    switch (type) {
      case 'full':
        // Validate complete configuration
        validation = await adminConfigService.validateConfiguration();
        break;

      case 'section':
        // Validate specific configuration section
        if (!data) {
          return NextResponse.json(
            { error: 'Configuration data is required for section validation' },
            { status: 400 }
          );
        }
        validation = await configManager.validate(data);
        break;

      case 'path':
        // Validate specific configuration path
        if (!path) {
          return NextResponse.json(
            { error: 'Configuration path is required for path validation' },
            { status: 400 }
          );
        }
        validation = await configManager.validatePath(path, value);
        break;

      case 'providers':
        // Test all providers
        const providerResults = await adminConfigService.testProviders();
        
        validation = {
          isValid: Object.values(providerResults).every(result => result.success),
          errors: Object.entries(providerResults)
            .filter(([_, result]) => !result.success)
            .map(([provider, result]) => ({
              path: provider,
              message: result.error || `${provider} provider test failed`,
              value: null,
              rule: { path: provider, type: 'object', required: true }
            })),
          warnings: [],
          score: Object.values(providerResults).filter(result => result.success).length / Object.keys(providerResults).length * 100,
          providerResults
        };
        break;

      default:
        return NextResponse.json(
          { error: `Unknown validation type: ${type}` },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      validation,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Configuration validation error:', error);
    return NextResponse.json(
      { 
        error: 'Configuration validation failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET /api/admin/config/validate - Get validation status
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    if (!checkAdminAuth(request)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const includeProviders = searchParams.get('includeProviders') === 'true';

    // Wait for configuration manager to be ready
    await configManager.waitForInitialization();

    // Get current validation status
    const validation = await adminConfigService.validateConfiguration();
    const summary = adminConfigService.getConfigurationSummary();

    let providerStatus = null;
    if (includeProviders) {
      providerStatus = await adminConfigService.testProviders();
    }

    return NextResponse.json({
      success: true,
      validation,
      summary,
      providerStatus,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Configuration validation status error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get validation status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
