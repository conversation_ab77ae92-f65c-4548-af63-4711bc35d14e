// Environment Configuration Loader

import { EnvironmentConfiguration, ValidationError, DEFAULT_ENVIRONMENT_CONFIG } from './types';

export class EnvironmentConfigLoader {
  private config: EnvironmentConfiguration;
  private validationErrors: ValidationError[] = [];

  constructor() {
    this.loadConfiguration();
    this.validateConfiguration();
  }

  private loadConfiguration(): void {
    this.config = {
      // Core System
      NODE_ENV: (process.env.NODE_ENV as any) || 'development',
      PORT: parseInt(process.env.PORT || '3000'),
      SITE_URL: process.env.SITE_URL || 'http://localhost:3000',
      
      // Database
      SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL!,
      SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY!,
      DATABASE_URL: process.env.DATABASE_URL!,
      
      // External APIs
      SCRAPE_DO_API_KEY: process.env.SCRAPE_DO_API_KEY!,
      SCRAPE_DO_BASE_URL: process.env.SCRAPE_DO_BASE_URL || 'https://api.scrape.do',
      OPENAI_API_KEY: process.env.OPENAI_API_KEY!,
      OPENAI_ORGANIZATION: process.env.OPENAI_ORGANIZATION,
      OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY!,
      OPENROUTER_BASE_URL: process.env.OPENROUTER_BASE_URL || 'https://openrouter.ai/api/v1',
      
      // Job Processing
      JOB_QUEUE_REDIS_URL: process.env.JOB_QUEUE_REDIS_URL,
      JOB_CONCURRENCY: parseInt(process.env.JOB_CONCURRENCY || '3'),
      JOB_TIMEOUT: parseInt(process.env.JOB_TIMEOUT || '300000'),
      JOB_RETRY_ATTEMPTS: parseInt(process.env.JOB_RETRY_ATTEMPTS || '3'),
      
      // Security
      ADMIN_API_KEY: process.env.ADMIN_API_KEY!,
      JWT_SECRET: process.env.JWT_SECRET!,
      ENCRYPTION_KEY: process.env.ENCRYPTION_KEY || this.generateEncryptionKey(),
      
      // Email
      SMTP_HOST: process.env.SMTP_HOST,
      SMTP_PORT: process.env.SMTP_PORT ? parseInt(process.env.SMTP_PORT) : undefined,
      SMTP_USER: process.env.SMTP_USER,
      SMTP_PASS: process.env.SMTP_PASS,
      
      // Analytics
      GOOGLE_ANALYTICS_ID: process.env.GOOGLE_ANALYTICS_ID,
      
      // Feature Flags
      CONTENT_GENERATION_ENABLED: process.env.CONTENT_GENERATION_ENABLED === 'true',
      JOB_QUEUE_ENABLED: process.env.JOB_QUEUE_ENABLED === 'true',
      DEBUG_MODE: process.env.DEBUG_MODE === 'true',
      MAINTENANCE_MODE: process.env.MAINTENANCE_MODE === 'true'
    };
  }

  private validateConfiguration(): void {
    this.validationErrors = [];

    // Required environment variables
    const requiredVars = [
      'SUPABASE_URL',
      'SUPABASE_ANON_KEY', 
      'SUPABASE_SERVICE_ROLE_KEY',
      'DATABASE_URL',
      'SCRAPE_DO_API_KEY',
      'OPENAI_API_KEY',
      'OPENROUTER_API_KEY',
      'ADMIN_API_KEY',
      'JWT_SECRET'
    ];

    for (const varName of requiredVars) {
      const value = this.config[varName as keyof EnvironmentConfiguration];
      if (!value || (typeof value === 'string' && value.trim() === '')) {
        this.validationErrors.push({
          path: varName,
          message: `Required environment variable ${varName} is missing or empty`,
          value,
          rule: {
            path: varName,
            type: 'string',
            required: true
          }
        });
      }
    }

    // Validate URLs
    this.validateUrl('SITE_URL', this.config.SITE_URL);
    this.validateUrl('SUPABASE_URL', this.config.SUPABASE_URL);
    this.validateUrl('SCRAPE_DO_BASE_URL', this.config.SCRAPE_DO_BASE_URL);
    this.validateUrl('OPENROUTER_BASE_URL', this.config.OPENROUTER_BASE_URL);

    // Validate numeric values
    this.validateNumber('PORT', this.config.PORT, 1, 65535);
    this.validateNumber('JOB_CONCURRENCY', this.config.JOB_CONCURRENCY, 1, 20);
    this.validateNumber('JOB_TIMEOUT', this.config.JOB_TIMEOUT, 1000, 600000);
    this.validateNumber('JOB_RETRY_ATTEMPTS', this.config.JOB_RETRY_ATTEMPTS, 0, 10);

    if (this.config.SMTP_PORT) {
      this.validateNumber('SMTP_PORT', this.config.SMTP_PORT, 1, 65535);
    }

    // Validate environment type
    const validEnvs = ['development', 'staging', 'production'];
    if (!validEnvs.includes(this.config.NODE_ENV)) {
      this.validationErrors.push({
        path: 'NODE_ENV',
        message: `NODE_ENV must be one of: ${validEnvs.join(', ')}`,
        value: this.config.NODE_ENV,
        rule: {
          path: 'NODE_ENV',
          type: 'string',
          required: true,
          enum: validEnvs
        }
      });
    }

    // Validate API keys format
    this.validateApiKey('OPENAI_API_KEY', this.config.OPENAI_API_KEY, /^sk-/);
    this.validateApiKey('OPENROUTER_API_KEY', this.config.OPENROUTER_API_KEY, /^sk-or-/);

    // Validate JWT secret strength
    if (this.config.JWT_SECRET && this.config.JWT_SECRET.length < 32) {
      this.validationErrors.push({
        path: 'JWT_SECRET',
        message: 'JWT_SECRET should be at least 32 characters long for security',
        value: '***',
        rule: {
          path: 'JWT_SECRET',
          type: 'string',
          required: true,
          min: 32
        }
      });
    }
  }

  private validateUrl(name: string, url: string): void {
    if (!url) return;
    
    try {
      new URL(url);
    } catch (error) {
      this.validationErrors.push({
        path: name,
        message: `${name} is not a valid URL`,
        value: url,
        rule: {
          path: name,
          type: 'string',
          required: true,
          pattern: /^https?:\/\/.+/
        }
      });
    }
  }

  private validateNumber(name: string, value: number, min?: number, max?: number): void {
    if (isNaN(value)) {
      this.validationErrors.push({
        path: name,
        message: `${name} must be a valid number`,
        value,
        rule: {
          path: name,
          type: 'number',
          required: true
        }
      });
      return;
    }

    if (min !== undefined && value < min) {
      this.validationErrors.push({
        path: name,
        message: `${name} must be at least ${min}`,
        value,
        rule: {
          path: name,
          type: 'number',
          required: true,
          min
        }
      });
    }

    if (max !== undefined && value > max) {
      this.validationErrors.push({
        path: name,
        message: `${name} must be at most ${max}`,
        value,
        rule: {
          path: name,
          type: 'number',
          required: true,
          max
        }
      });
    }
  }

  private validateApiKey(name: string, key: string, pattern: RegExp): void {
    if (!key) return;
    
    if (!pattern.test(key)) {
      this.validationErrors.push({
        path: name,
        message: `${name} does not match expected format`,
        value: '***',
        rule: {
          path: name,
          type: 'string',
          required: true,
          pattern
        }
      });
    }
  }

  private generateEncryptionKey(): string {
    // Generate a random 32-byte key for encryption
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 64; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  // Public methods
  public getConfiguration(): EnvironmentConfiguration {
    return { ...this.config };
  }

  public getValidationErrors(): ValidationError[] {
    return [...this.validationErrors];
  }

  public isValid(): boolean {
    return this.validationErrors.length === 0;
  }

  public getConfigurationSummary(): {
    isValid: boolean;
    errorCount: number;
    warningCount: number;
    environment: string;
    featuresEnabled: string[];
  } {
    const featuresEnabled = [];
    if (this.config.CONTENT_GENERATION_ENABLED) featuresEnabled.push('Content Generation');
    if (this.config.JOB_QUEUE_ENABLED) featuresEnabled.push('Job Queue');
    if (this.config.DEBUG_MODE) featuresEnabled.push('Debug Mode');
    if (this.config.MAINTENANCE_MODE) featuresEnabled.push('Maintenance Mode');

    return {
      isValid: this.isValid(),
      errorCount: this.validationErrors.length,
      warningCount: 0, // Could add warnings for non-critical issues
      environment: this.config.NODE_ENV,
      featuresEnabled
    };
  }

  public validateEnvironmentVariable(name: string, value: string): ValidationError[] {
    const errors: ValidationError[] = [];
    
    // Create a temporary config with the new value
    const tempConfig = { ...this.config, [name]: value };
    
    // Re-run validation for this specific variable
    const originalErrors = this.validationErrors;
    this.config = tempConfig as EnvironmentConfiguration;
    this.validateConfiguration();
    
    // Get errors for this specific variable
    const newErrors = this.validationErrors.filter(error => error.path === name);
    
    // Restore original config and errors
    this.config = { ...this.config, ...originalErrors };
    this.validationErrors = originalErrors;
    
    return newErrors;
  }

  public getMissingRequiredVariables(): string[] {
    return this.validationErrors
      .filter(error => error.message.includes('missing or empty'))
      .map(error => error.path);
  }

  public getSecurityIssues(): ValidationError[] {
    return this.validationErrors.filter(error => 
      error.path.includes('SECRET') || 
      error.path.includes('KEY') || 
      error.path.includes('PASS')
    );
  }
}
