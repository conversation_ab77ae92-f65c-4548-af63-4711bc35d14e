// Central Configuration Management System

import { EventEmitter } from 'events';
import { 
  SystemConfiguration, 
  AdminPanelConfiguration, 
  EnvironmentConfiguration,
  ConfigurationManagerInterface,
  ConfigurationChange,
  ValidationResult,
  ConfigurationSource,
  ConfigurationCache,
  DEFAULT_ADMIN_CONFIG
} from './types';
import { EnvironmentConfigLoader } from './environment-loader';
import { ConfigurationEncryption } from './encryption';
import { ConfigurationValidator } from './validation';
import { createClient } from '@supabase/supabase-js';

export class ConfigurationManager extends EventEmitter implements ConfigurationManagerInterface {
  private environmentConfig: EnvironmentConfiguration;
  private adminConfig: AdminPanelConfiguration;
  private mergedConfig: SystemConfiguration;
  private cache: ConfigurationCache;
  private encryption: ConfigurationEncryption;
  private validator: ConfigurationValidator;
  private supabase: ReturnType<typeof createClient>;
  private changes: ConfigurationChange[] = [];
  private initialized = false;

  constructor() {
    super();
    this.initializeCache();
    this.encryption = new ConfigurationEncryption();
    this.validator = new ConfigurationValidator();
    this.initialize();
  }

  private initializeCache(): void {
    this.cache = {
      data: new Map(),
      timestamps: new Map(),
      ttl: 300000, // 5 minutes
      maxSize: 1000
    };
  }

  private async initialize(): Promise<void> {
    try {
      // Load environment configuration
      const envLoader = new EnvironmentConfigLoader();
      this.environmentConfig = envLoader.getConfiguration();

      // Initialize Supabase client
      this.supabase = createClient(
        this.environmentConfig.SUPABASE_URL,
        this.environmentConfig.SUPABASE_SERVICE_ROLE_KEY
      );

      // Load admin configuration from database
      await this.loadAdminConfiguration();

      // Merge configurations
      this.mergeConfigurations();

      // Start configuration watcher
      this.startConfigurationWatcher();

      this.initialized = true;
      this.emit('initialized');
    } catch (error) {
      console.error('Configuration manager initialization failed:', error);
      this.emit('error', error);
    }
  }

  private async loadAdminConfiguration(): Promise<void> {
    try {
      const { data, error } = await this.supabase
        .from('system_configuration')
        .select('*')
        .eq('environment', this.environmentConfig.NODE_ENV)
        .single();

      if (error && error.code !== 'PGRST116') { // Not found error
        throw error;
      }

      if (data && data.configuration) {
        // Decrypt sensitive fields
        const decryptedConfig = this.encryption.decryptSensitiveFields(data.configuration);
        this.adminConfig = { ...DEFAULT_ADMIN_CONFIG, ...decryptedConfig };
      } else {
        // Use default configuration and save it
        this.adminConfig = { ...DEFAULT_ADMIN_CONFIG };
        await this.saveAdminConfiguration();
      }
    } catch (error) {
      console.warn('Failed to load admin configuration, using defaults:', error);
      this.adminConfig = { ...DEFAULT_ADMIN_CONFIG };
    }
  }

  private async saveAdminConfiguration(): Promise<void> {
    try {
      // Encrypt sensitive fields before saving
      const encryptedConfig = this.encryption.encryptSensitiveFields(this.adminConfig);

      const { error } = await this.supabase
        .from('system_configuration')
        .upsert({
          environment: this.environmentConfig.NODE_ENV,
          configuration: encryptedConfig,
          updated_at: new Date().toISOString()
        });

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Failed to save admin configuration:', error);
      throw error;
    }
  }

  private mergeConfigurations(): void {
    // Environment variables take precedence over admin panel settings
    this.mergedConfig = {
      ...this.adminConfig,
      ...this.environmentConfig,
      _metadata: {
        lastUpdated: new Date().toISOString(),
        version: '1.0.0',
        source: 'merged' as ConfigurationSource,
        environment: this.environmentConfig.NODE_ENV
      }
    };

    // Clear cache when configuration changes
    this.clearCache();
  }

  private startConfigurationWatcher(): void {
    // Watch for database changes (simplified - in production, use real-time subscriptions)
    setInterval(async () => {
      try {
        const { data } = await this.supabase
          .from('system_configuration')
          .select('updated_at')
          .eq('environment', this.environmentConfig.NODE_ENV)
          .single();

        if (data && data.updated_at !== this.mergedConfig._metadata.lastUpdated) {
          await this.reload();
        }
      } catch {
        // Silently handle errors in watcher
      }
    }, 30000); // Check every 30 seconds
  }

  // Public API Implementation

  public get<T>(path: string): T {
    // Check cache first
    const cacheKey = `get:${path}`;
    if (this.isCacheValid(cacheKey)) {
      return this.cache.data.get(cacheKey);
    }

    // Get value from merged configuration
    const value = this.getNestedValue(this.mergedConfig, path);

    // Cache the value
    this.setCache(cacheKey, value);

    return value;
  }

  public async set(path: string, value: unknown, source: ConfigurationSource = 'admin'): Promise<void> {
    // Validate the new value
    const validation = await this.validatePath(path, value);
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.map(e => e.message).join(', ')}`);
    }

    // Record the change
    const oldValue = this.getNestedValue(this.mergedConfig, path);
    const change: ConfigurationChange = {
      id: this.generateChangeId(),
      path,
      oldValue,
      newValue: value,
      source,
      timestamp: new Date().toISOString(),
      rollbackData: { path, value: oldValue }
    };

    this.changes.push(change);

    // Update configuration
    if (source === 'admin') {
      this.setNestedValue(this.adminConfig, path, value);
      await this.saveAdminConfiguration();
    }

    // Update merged configuration
    this.setNestedValue(this.mergedConfig, path, value);

    // Clear cache
    this.clearCache();

    // Emit change event
    this.emit('change', { path, value, oldValue, source });
  }

  public has(path: string): boolean {
    return this.getNestedValue(this.mergedConfig, path) !== undefined;
  }

  public async delete(path: string): Promise<void> {
    const oldValue = this.getNestedValue(this.mergedConfig, path);
    
    // Record the change
    const change: ConfigurationChange = {
      id: this.generateChangeId(),
      path,
      oldValue,
      newValue: undefined,
      source: 'admin',
      timestamp: new Date().toISOString(),
      rollbackData: { path, value: oldValue }
    };

    this.changes.push(change);

    // Remove from admin config
    this.deleteNestedValue(this.adminConfig, path);
    await this.saveAdminConfiguration();

    // Update merged configuration
    this.mergeConfigurations();

    // Clear cache
    this.clearCache();

    // Emit change event
    this.emit('change', { path, value: undefined, oldValue, source: 'admin' });
  }

  public async validate(config?: Partial<SystemConfiguration>): Promise<ValidationResult> {
    const configToValidate = config || this.mergedConfig;
    return this.validator.validate(configToValidate);
  }

  public async validatePath(path: string, value: unknown): Promise<ValidationResult> {
    return this.validator.validatePath(path, value);
  }

  public async reload(): Promise<void> {
    await this.loadAdminConfiguration();
    this.mergeConfigurations();
    this.emit('reload');
  }

  public export(): SystemConfiguration {
    return JSON.parse(JSON.stringify(this.mergedConfig));
  }

  public async import(config: Partial<SystemConfiguration>): Promise<ValidationResult> {
    const validation = await this.validate(config);
    
    if (!validation.isValid) {
      return validation;
    }

    // Update admin configuration with imported values
    Object.assign(this.adminConfig, config);
    await this.saveAdminConfiguration();
    this.mergeConfigurations();

    this.emit('import', config);
    return validation;
  }

  public getChanges(since?: string): ConfigurationChange[] {
    if (!since) {
      return [...this.changes];
    }

    const sinceDate = new Date(since);
    return this.changes.filter(change => new Date(change.timestamp) > sinceDate);
  }

  public async rollback(changeId: string): Promise<void> {
    const change = this.changes.find(c => c.id === changeId);
    if (!change || !change.rollbackData) {
      throw new Error('Change not found or rollback data unavailable');
    }

    await this.set(change.rollbackData.path, change.rollbackData.value, 'admin');
  }

  public async testProvider(provider: 'openai' | 'openrouter' | 'scrape-do'): Promise<boolean> {
    try {
      switch (provider) {
        case 'openai':
          return await this.testOpenAI();
        case 'openrouter':
          return await this.testOpenRouter();
        case 'scrape-do':
          return await this.testScrapeDoAPI();
        default:
          return false;
      }
    } catch (error) {
      console.error(`Provider test failed for ${provider}:`, error);
      return false;
    }
  }

  public async testAllProviders(): Promise<Record<string, boolean>> {
    const results = await Promise.allSettled([
      this.testProvider('openai'),
      this.testProvider('openrouter'),
      this.testProvider('scrape-do')
    ]);

    return {
      openai: results[0].status === 'fulfilled' ? results[0].value : false,
      openrouter: results[1].status === 'fulfilled' ? results[1].value : false,
      'scrape-do': results[2].status === 'fulfilled' ? results[2].value : false
    };
  }

  // Helper methods

  private getNestedValue(obj: Record<string, unknown>, path: string): unknown {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  private setNestedValue(obj: Record<string, unknown>, path: string, value: unknown): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    const target = keys.reduce((current, key) => {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      return current[key];
    }, obj);
    target[lastKey] = value;
  }

  private deleteNestedValue(obj: Record<string, unknown>, path: string): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    const target = keys.reduce((current, key) => {
      return current && current[key] ? current[key] : null;
    }, obj);
    if (target) {
      delete target[lastKey];
    }
  }

  private isCacheValid(key: string): boolean {
    if (!this.cache.data.has(key)) return false;
    
    const timestamp = this.cache.timestamps.get(key);
    if (!timestamp) return false;
    
    return Date.now() - timestamp < this.cache.ttl;
  }

  private setCache(key: string, value: unknown): void {
    // Implement LRU eviction if cache is full
    if (this.cache.data.size >= this.cache.maxSize) {
      const oldestKey = this.cache.data.keys().next().value;
      this.cache.data.delete(oldestKey);
      this.cache.timestamps.delete(oldestKey);
    }

    this.cache.data.set(key, value);
    this.cache.timestamps.set(key, Date.now());
  }

  private clearCache(): void {
    this.cache.data.clear();
    this.cache.timestamps.clear();
  }

  private generateChangeId(): string {
    return `change_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async testOpenAI(): Promise<boolean> {
    const apiKey = this.get<string>('OPENAI_API_KEY');
    if (!apiKey) return false;

    const response = await fetch('https://api.openai.com/v1/models', {
      headers: { 'Authorization': `Bearer ${apiKey}` }
    });
    return response.ok;
  }

  private async testOpenRouter(): Promise<boolean> {
    const apiKey = this.get<string>('OPENROUTER_API_KEY');
    if (!apiKey) return false;

    const response = await fetch('https://openrouter.ai/api/v1/models', {
      headers: { 'Authorization': `Bearer ${apiKey}` }
    });
    return response.ok;
  }

  private async testScrapeDoAPI(): Promise<boolean> {
    const apiKey = this.get<string>('SCRAPE_DO_API_KEY');
    if (!apiKey) return false;

    const response = await fetch(`https://api.scrape.do?token=${apiKey}&url=https://example.com`);
    return response.ok;
  }

  // Additional utility methods

  public getEnvironmentConfig(): EnvironmentConfiguration {
    return { ...this.environmentConfig };
  }

  public getAdminConfig(): AdminPanelConfiguration {
    return { ...this.adminConfig };
  }

  public getMergedConfig(): SystemConfiguration {
    return { ...this.mergedConfig };
  }

  public isInitialized(): boolean {
    return this.initialized;
  }

  public async waitForInitialization(): Promise<void> {
    if (this.initialized) return;

    return new Promise((resolve) => {
      this.once('initialized', resolve);
    });
  }

  public getConfigurationSummary(): {
    environment: string;
    providersEnabled: string[];
    featuresEnabled: string[];
    lastUpdated: string;
    validationScore: number;
  } {
    const openaiEnabled = this.get<boolean>('aiGeneration.providers.openai.enabled');
    const openrouterEnabled = this.get<boolean>('aiGeneration.providers.openrouter.enabled');

    const providersEnabled = [];
    if (openaiEnabled) providersEnabled.push('OpenAI');
    if (openrouterEnabled) providersEnabled.push('OpenRouter');

    const featuresEnabled = [];
    if (this.get<boolean>('CONTENT_GENERATION_ENABLED')) featuresEnabled.push('Content Generation');
    if (this.get<boolean>('JOB_QUEUE_ENABLED')) featuresEnabled.push('Job Queue');
    if (this.get<boolean>('scraping.scrapeDoConfig.costOptimization.enabled')) featuresEnabled.push('Cost Optimization');
    if (this.get<boolean>('system.performance.cacheEnabled')) featuresEnabled.push('Caching');

    return {
      environment: this.get<string>('NODE_ENV'),
      providersEnabled,
      featuresEnabled,
      lastUpdated: this.get<string>('_metadata.lastUpdated'),
      validationScore: 100 // Will be calculated by validation
    };
  }

  public async exportConfiguration(includeSecrets = false): Promise<string> {
    const config = this.export();

    if (!includeSecrets) {
      // Remove sensitive fields
      const sanitized = this.sanitizeConfiguration(config);
      return JSON.stringify(sanitized, null, 2);
    }

    return JSON.stringify(config, null, 2);
  }

  private sanitizeConfiguration(config: Record<string, unknown>): Record<string, unknown> {
    const sensitiveFields = [
      'OPENAI_API_KEY',
      'OPENROUTER_API_KEY',
      'SCRAPE_DO_API_KEY',
      'ADMIN_API_KEY',
      'JWT_SECRET',
      'ENCRYPTION_KEY',
      'SMTP_PASS',
      'DATABASE_URL'
    ];

    const sanitized = JSON.parse(JSON.stringify(config));

    const sanitizeObject = (obj: Record<string, unknown>): Record<string, unknown> => {
      for (const [key, value] of Object.entries(obj)) {
        if (sensitiveFields.some(field => key.includes(field))) {
          obj[key] = '***REDACTED***';
        } else if (typeof value === 'object' && value !== null) {
          sanitizeObject(value);
        }
      }
      return obj;
    };

    return sanitizeObject(sanitized);
  }

  // Singleton pattern
  private static instance: ConfigurationManager;

  public static getInstance(): ConfigurationManager {
    if (!ConfigurationManager.instance) {
      ConfigurationManager.instance = new ConfigurationManager();
    }
    return ConfigurationManager.instance;
  }
}

// Export singleton instance
export const configManager = ConfigurationManager.getInstance();
