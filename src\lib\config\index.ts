// Configuration Management System - Main Exports

// Core Configuration Manager
export { ConfigurationManager, configManager } from './configuration-manager';

// Configuration Services
export { AdminConfigurationService, adminConfigService } from './admin-config';
export { AIConfigurationIntegration, aiConfigIntegration } from './ai-integration';

// Utility Classes
export { EnvironmentConfigLoader } from './environment-loader';
export { ConfigurationEncryption } from './encryption';
export { ConfigurationValidator } from './validation';

// Types and Interfaces
export type {
  // Core Types
  ConfigurationSource,
  ConfigurationLevel,
  EnvironmentType,
  
  // Configuration Interfaces
  EnvironmentConfiguration,
  AdminPanelConfiguration,
  SystemConfiguration,
  
  // Validation Types
  ValidationRule,
  ValidationError,
  ValidationResult,
  
  // Change Tracking
  ConfigurationChange,
  ConfigurationPermission,
  
  // Manager Interface
  ConfigurationManagerInterface,
  
  // Schema and Cache
  ConfigurationSchema,
  ConfigurationCache,
  ConfigurationExport,
  
  // Encryption
  EncryptionConfig
} from './types';

// Default Configurations
export {
  DEFAULT_ENVIRONMENT_CONFIG,
  DEFAULT_ADMIN_CONFIG
} from './types';

// Utility Functions
export const ConfigUtils = {
  /**
   * Validate environment variables
   */
  validateEnvironment: (): { valid: boolean; errors: string[] } => {
    const loader = new EnvironmentConfigLoader();
    return {
      valid: loader.isValid(),
      errors: loader.getValidationErrors().map(e => e.message)
    };
  },

  /**
   * Get configuration summary
   */
  getConfigurationSummary: () => {
    return configManager.getConfigurationSummary();
  },

  /**
   * Test all providers
   */
  testAllProviders: async () => {
    return configManager.testAllProviders();
  },

  /**
   * Export configuration
   */
  exportConfiguration: async (includeSecrets = false) => {
    return configManager.exportConfiguration(includeSecrets);
  },

  /**
   * Validate configuration
   */
  validateConfiguration: async () => {
    return configManager.validate();
  },

  /**
   * Get AI system summary
   */
  getAISystemSummary: () => {
    return aiConfigIntegration.getAISystemSummary();
  }
};

// Quick Access Functions
export const getConfig = <T>(path: string): T => {
  return configManager.get<T>(path);
};

export const setConfig = async (path: string, value: any): Promise<void> => {
  return configManager.set(path, value);
};

export const hasConfig = (path: string): boolean => {
  return configManager.has(path);
};

// Provider Configuration Helpers
export const getAIProviderConfig = (provider: 'openai' | 'openrouter') => {
  return aiConfigIntegration.getProviderConfig(provider);
};

export const getPreferredAIProvider = () => {
  return aiConfigIntegration.getPreferredProvider();
};

export const getAvailableAIProviders = () => {
  return aiConfigIntegration.getAvailableProviders();
};

// Configuration Events
export const onConfigurationChange = (callback: (path: string, value: any) => void) => {
  configManager.on('change', ({ path, value }) => callback(path, value));
};

export const onConfigurationError = (callback: (error: Error) => void) => {
  configManager.on('error', callback);
};

// System Health
export const getSystemHealth = async () => {
  const validation = await configManager.validate();
  const providerStatus = await configManager.testAllProviders();
  const summary = configManager.getConfigurationSummary();

  return {
    overall: validation.isValid && Object.values(providerStatus).every(status => status) ? 'healthy' : 'degraded',
    configuration: {
      valid: validation.isValid,
      score: validation.score,
      errors: validation.errors.length,
      warnings: validation.warnings.length
    },
    providers: providerStatus,
    summary
  };
};

// Configuration Constants
export const CONFIG_PATHS = {
  // AI Providers
  OPENAI_ENABLED: 'aiGeneration.providers.openai.enabled',
  OPENAI_MODEL: 'aiGeneration.providers.openai.model',
  OPENAI_MAX_TOKENS: 'aiGeneration.providers.openai.maxTokens',
  OPENAI_TEMPERATURE: 'aiGeneration.providers.openai.temperature',
  
  OPENROUTER_ENABLED: 'aiGeneration.providers.openrouter.enabled',
  OPENROUTER_MODEL: 'aiGeneration.providers.openrouter.model',
  OPENROUTER_MAX_TOKENS: 'aiGeneration.providers.openrouter.maxTokens',
  OPENROUTER_TEMPERATURE: 'aiGeneration.providers.openrouter.temperature',
  
  // Model Selection
  MODEL_SELECTION_STRATEGY: 'aiGeneration.modelSelection.strategy',
  MODEL_FALLBACK_ORDER: 'aiGeneration.modelSelection.fallbackOrder',
  MODEL_COST_THRESHOLD: 'aiGeneration.modelSelection.costThreshold',
  MODEL_QUALITY_THRESHOLD: 'aiGeneration.modelSelection.qualityThreshold',
  
  // Content Generation
  CONTENT_AUTO_APPROVAL: 'aiGeneration.contentGeneration.autoApproval',
  CONTENT_QUALITY_THRESHOLD: 'aiGeneration.contentGeneration.qualityThreshold',
  CONTENT_EDITORIAL_REVIEW: 'aiGeneration.contentGeneration.editorialReviewRequired',
  
  // System Settings
  SYSTEM_DEBUG_MODE: 'system.debugMode',
  SYSTEM_MAINTENANCE_MODE: 'system.maintenanceMode',
  SYSTEM_CACHE_ENABLED: 'system.performance.cacheEnabled',
  SYSTEM_RATE_LIMITING: 'system.performance.rateLimiting',
  
  // Environment
  NODE_ENV: 'NODE_ENV',
  SITE_URL: 'SITE_URL',
  OPENAI_API_KEY: 'OPENAI_API_KEY',
  OPENROUTER_API_KEY: 'OPENROUTER_API_KEY',
  SCRAPE_DO_API_KEY: 'SCRAPE_DO_API_KEY'
} as const;

// Version Information
export const CONFIG_SYSTEM_INFO = {
  version: '1.0.0',
  features: [
    'Environment-based Configuration',
    'Admin Panel Interface',
    'Secure Encryption (AES-256-GCM)',
    'Real-time Validation',
    'Hot-reload Updates',
    'Export/Import Functionality',
    'Audit Logging',
    'Role-based Access Control',
    'Multi-environment Support',
    'AI System Integration'
  ],
  components: [
    'Configuration Manager',
    'Environment Loader',
    'Encryption Service',
    'Validation Engine',
    'Admin Service',
    'AI Integration',
    'API Endpoints',
    'Admin UI Components'
  ]
};

// Initialize configuration manager on module load
configManager.waitForInitialization().catch(error => {
  console.error('Configuration manager initialization failed:', error);
});
